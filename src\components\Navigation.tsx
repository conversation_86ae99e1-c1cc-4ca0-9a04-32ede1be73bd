'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import {
  Package,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Home,
  Store,
  Menu,
  X,
  ChevronRight,
  Settings,
  LogOut,
  Bell,
  Search,
  User,
  Maximize2,
  Minimize2
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home, description: 'Overview & analytics' },
  { name: 'Products', href: '/products', icon: Package, description: 'Manage inventory' },
  { name: 'Customers', href: '/customers', icon: Users, description: 'Customer database' },
  { name: 'Debts', href: '/debts', icon: CreditCard, description: 'Track customer debts' },
  { name: 'Payments', href: '/payments', icon: Receipt, description: 'Payment records' },
  { name: 'Reports', href: '/reports', icon: BarChart3, description: 'Business insights' },
]

interface NavigationProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
}

export default function Navigation({ isCollapsed = false, onToggleCollapse }: NavigationProps) {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-white p-2.5 rounded-xl shadow-lg border border-gray-200 hover:bg-gray-50 transition-all duration-200"
        >
          {isMobileMenuOpen ? (
            <X className="h-5 w-5 text-gray-700" />
          ) : (
            <Menu className="h-5 w-5 text-gray-700" />
          )}
        </button>
      </div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Navigation Sidebar */}
      <nav className={`
        fixed lg:static inset-y-0 left-0 z-50 bg-white border-r border-gray-200
        transform transition-all duration-300 ease-in-out lg:transform-none
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}>
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b border-gray-100 ${isCollapsed ? 'px-3' : 'px-6'}`}>
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-2 rounded-lg shadow-sm">
                <Store className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">
                  Caparan Tindahan
                </h1>
                <p className="text-xs text-gray-500">Admin Dashboard</p>
              </div>
            </div>
          )}

          {isCollapsed && (
            <div className="flex items-center justify-center w-full">
              <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-2 rounded-lg shadow-sm">
                <Store className="h-6 w-6 text-white" />
              </div>
            </div>
          )}

          {/* Collapse Toggle - Desktop Only */}
          <button
            onClick={onToggleCollapse}
            className="hidden lg:flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            {isCollapsed ? (
              <Maximize2 className="h-4 w-4 text-gray-500" />
            ) : (
              <Minimize2 className="h-4 w-4 text-gray-500" />
            )}
          </button>
        </div>

        {/* Navigation Links */}
        <div className="flex-1 py-4">
          <ul className={`space-y-1 ${isCollapsed ? 'px-2' : 'px-3'}`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`
                      group flex items-center rounded-lg transition-all duration-200 relative
                      ${isCollapsed ? 'p-3 justify-center' : 'px-3 py-2.5'}
                      ${isActive
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                      }
                    `}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <div className={`
                      flex items-center justify-center transition-colors
                      ${isCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'}
                      ${isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}
                    `}>
                      <item.icon className="w-full h-full" />
                    </div>

                    {!isCollapsed && (
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{item.name}</div>
                        <div className="text-xs text-gray-500 truncate">{item.description}</div>
                      </div>
                    )}

                    {isActive && !isCollapsed && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    )}

                    {/* Tooltip for collapsed state */}
                    {isCollapsed && (
                      <div className="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
                        <div className="font-medium">{item.name}</div>
                        <div className="text-xs text-gray-300">{item.description}</div>
                        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"></div>
                      </div>
                    )}
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {/* Footer */}
        <div className={`border-t border-gray-100 ${isCollapsed ? 'p-2' : 'p-4'}`}>
          <div className={`space-y-1 ${isCollapsed ? '' : 'space-y-2'}`}>
            <button className={`
              w-full flex items-center text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors
              ${isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'}
            `} title={isCollapsed ? 'Settings' : undefined}>
              <Settings className={`text-gray-500 ${isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'}`} />
              {!isCollapsed && 'Settings'}
            </button>
            <button className={`
              w-full flex items-center text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors
              ${isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'}
            `} title={isCollapsed ? 'Sign Out' : undefined}>
              <LogOut className={`text-gray-500 ${isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'}`} />
              {!isCollapsed && 'Sign Out'}
            </button>
          </div>

          {/* Store Info - Only show when not collapsed */}
          {!isCollapsed && (
            <div className="mt-4 p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
              <div className="text-xs font-medium text-gray-900">Store Status</div>
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <span className="text-xs text-gray-600">Online & Active</span>
              </div>
            </div>
          )}
        </div>
      </nav>
    </>
  )
}

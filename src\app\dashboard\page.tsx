'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Package, Users, CreditCard, TrendingUp, AlertTriangle, Clock, DollarSign } from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  totalCustomers: number
  totalDebts: number
  totalRevenue: number
}

interface RecentActivity {
  id: string
  type: 'debt' | 'payment' | 'product'
  description: string
  amount?: number
  timestamp: string
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCustomers: 0,
    totalDebts: 0,
    totalRevenue: 0,
  })
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch products
        const productsRes = await fetch('/api/products')
        const products = await productsRes.json()

        // Fetch customers
        const customersRes = await fetch('/api/customers')
        const customers = await customersRes.json()

        // Fetch debts
        const debtsRes = await fetch('/api/debts')
        const debts = await debtsRes.json()

        // Fetch payments
        const paymentsRes = await fetch('/api/payments')
        const payments = await paymentsRes.json()

        const totalRevenue = payments.reduce((sum: number, payment: any) => sum + payment.amount, 0)
        const totalDebts = debts.reduce((sum: number, debt: any) => sum + debt.totalAmount, 0)

        setStats({
          totalProducts: products.length,
          totalCustomers: customers.length,
          totalDebts,
          totalRevenue,
        })

        // Generate recent activity
        const activities: RecentActivity[] = [
          ...payments.slice(0, 3).map((payment: any) => ({
            id: payment.id,
            type: 'payment' as const,
            description: `Payment received from ${payment.customer.firstName} ${payment.customer.lastName}`,
            amount: payment.amount,
            timestamp: payment.dateOfPayment,
          })),
          ...debts.slice(0, 2).map((debt: any) => ({
            id: debt.id,
            type: 'debt' as const,
            description: `New debt recorded for ${debt.customer.firstName} ${debt.customer.lastName}`,
            amount: debt.totalAmount,
            timestamp: debt.dateOfDebt,
          })),
        ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5)

        setRecentActivity(activities)
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Customers',
      value: stats.totalCustomers,
      icon: Users,
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Outstanding Debts',
      value: `₱${stats.totalDebts.toLocaleString()}`,
      icon: CreditCard,
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-700',
      change: '-5%',
      changeType: 'negative' as const,
    },
    {
      title: 'Total Revenue',
      value: `₱${stats.totalRevenue.toLocaleString()}`,
      icon: TrendingUp,
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      change: '+23%',
      changeType: 'positive' as const,
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <DollarSign className="h-4 w-4 text-green-600" />
      case 'debt':
        return <CreditCard className="h-4 w-4 text-orange-600" />
      case 'product':
        return <Package className="h-4 w-4 text-blue-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <DashboardLayout
      title="Dashboard"
      subtitle="Welcome back! Here's what's happening with your store today."
      showSearch={true}
      actions={
        <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          Quick Sale
        </button>
      }
    >
      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((card, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center justify-between mb-4">
              <div className={`${card.bgColor} p-3 rounded-lg`}>
                <card.icon className={`h-6 w-6 ${card.textColor}`} />
              </div>
              <div className={`text-sm font-medium ${
                card.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {card.change}
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">{card.title}</p>
              <p className="text-2xl font-bold text-gray-900">
                {loading ? '...' : card.value}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions - Enhanced */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <a
                href="/products/new"
                className="group block p-4 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-xl transition-all duration-200 border border-blue-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Package className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-blue-900">Add New Product</div>
                    <div className="text-sm text-blue-700">Expand your inventory</div>
                  </div>
                </div>
              </a>
              
              <a
                href="/customers/new"
                className="group block p-4 bg-gradient-to-r from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 rounded-xl transition-all duration-200 border border-green-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-green-900">Add New Customer</div>
                    <div className="text-sm text-green-700">Register new customer</div>
                  </div>
                </div>
              </a>
              
              <a
                href="/debts/new"
                className="group block p-4 bg-gradient-to-r from-orange-50 to-orange-100 hover:from-orange-100 hover:to-orange-200 rounded-xl transition-all duration-200 border border-orange-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-orange-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <CreditCard className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-orange-900">Record New Debt</div>
                    <div className="text-sm text-orange-700">Track customer purchases</div>
                  </div>
                </div>
              </a>
              
              <a
                href="/payments/new"
                className="group block p-4 bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 rounded-xl transition-all duration-200 border border-purple-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <DollarSign className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-purple-900">Record Payment</div>
                    <div className="text-sm text-purple-700">Process customer payment</div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>

        {/* Recent Activity - Enhanced */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
          <div className="space-y-4">
            {loading ? (
              <div className="text-sm text-gray-500">Loading recent activity...</div>
            ) : recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-xs text-gray-500">
                        {formatDate(activity.timestamp)}
                      </p>
                      {activity.amount && (
                        <p className="text-sm font-semibold text-gray-900">
                          ₱{activity.amount.toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-gray-500 text-center py-4">
                No recent activity to display
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Business Insights */}
      <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Weekly Performance</h3>
          <div className="space-y-4">
            {/* Mock Chart Data */}
            {[
              { day: 'Mon', sales: 85, target: 100 },
              { day: 'Tue', sales: 92, target: 100 },
              { day: 'Wed', sales: 78, target: 100 },
              { day: 'Thu', sales: 95, target: 100 },
              { day: 'Fri', sales: 88, target: 100 },
              { day: 'Sat', sales: 110, target: 100 },
              { day: 'Sun', sales: 75, target: 100 },
            ].map((data, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className="w-8 text-sm font-medium text-gray-600">{data.day}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-3 relative">
                  <div
                    className={`h-3 rounded-full ${
                      data.sales >= data.target ? 'bg-green-500' : 'bg-blue-500'
                    }`}
                    style={{ width: `${Math.min(data.sales, 120)}%` }}
                  ></div>
                </div>
                <div className="w-16 text-sm font-medium text-gray-900">
                  {data.sales}%
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Selling Products</h3>
          <div className="space-y-4">
            {[
              { name: 'Lucky Me Instant Noodles', sales: 45, revenue: 675 },
              { name: 'Coca-Cola 350ml', sales: 38, revenue: 950 },
              { name: 'Skyflakes Crackers', sales: 32, revenue: 480 },
              { name: 'Maggi Magic Sarap', sales: 28, revenue: 420 },
              { name: 'Tide Detergent Powder', sales: 25, revenue: 1250 },
            ].map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">{product.name}</div>
                    <div className="text-xs text-gray-500">{product.sales} units sold</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-gray-900">₱{product.revenue}</div>
                  <div className="text-xs text-gray-500">revenue</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Alerts Section */}
      <div className="mt-6">
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-900">Store Alerts & Notifications</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Important updates and alerts for your store operations
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-900">Low Stock Items</div>
                <Package className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-yellow-600 mb-1">3</div>
              <div className="text-xs text-gray-500">Items need restocking</div>
              <div className="mt-2">
                <button className="text-xs text-yellow-700 hover:text-yellow-900 font-medium">
                  View Details →
                </button>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-900">Overdue Debts</div>
                <CreditCard className="h-4 w-4 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-600 mb-1">2</div>
              <div className="text-xs text-gray-500">Customers with overdue payments</div>
              <div className="mt-2">
                <button className="text-xs text-red-700 hover:text-red-900 font-medium">
                  Follow Up →
                </button>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-900">Today's Sales</div>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600 mb-1">₱2,450</div>
              <div className="text-xs text-gray-500">Revenue generated today</div>
              <div className="mt-2">
                <button className="text-xs text-green-700 hover:text-green-900 font-medium">
                  View Report →
                </button>
              </div>
            </div>
          </div>

          {/* Recent Alerts */}
          <div className="bg-white rounded-lg border border-yellow-200 p-4">
            <h4 className="font-medium text-gray-900 mb-3">Recent Notifications</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-3 text-sm">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <span className="text-gray-700">Lucky Me Instant Noodles is running low (5 units left)</span>
                <span className="text-gray-500 text-xs">2 hours ago</span>
              </div>
              <div className="flex items-center space-x-3 text-sm">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span className="text-gray-700">Customer payment reminder sent to Maria Santos</span>
                <span className="text-gray-500 text-xs">4 hours ago</span>
              </div>
              <div className="flex items-center space-x-3 text-sm">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-700">New product "Pancit Canton" added to inventory</span>
                <span className="text-gray-500 text-xs">6 hours ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
